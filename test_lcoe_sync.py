#!/usr/bin/env python3
"""
Test script to verify LCOE calculation synchronization
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from core.enhanced_financial_model import EnhancedAssumptions, build_enhanced_cashflow, compute_enhanced_kpis
from core.figure_generator_logic import generate_lcoe_waterfall_chart
import matplotlib.pyplot as plt

def test_lcoe_synchronization():
    """Test that all LCOE calculations are synchronized"""
    
    print("🔍 Testing LCOE Calculation Synchronization")
    print("=" * 50)
    
    # Create test assumptions
    assumptions = EnhancedAssumptions()
    
    # Build cashflow and compute KPIs
    cashflow_df = build_enhanced_cashflow(assumptions)
    kpis = compute_enhanced_kpis(cashflow_df, assumptions)
    
    # Get LCOE from enhanced model
    enhanced_lcoe = kpis.get('LCOE_eur_kwh', 0)
    
    print(f"📊 Enhanced Model LCOE: {enhanced_lcoe:.4f} EUR/kWh")
    
    # Calculate RAW LCOE using the same method as enhanced_main.py
    try:
        # Base parameters
        capex_total = assumptions.capex_meur * 1e6  # Convert to EUR
        capacity_mw = assumptions.capacity_mw
        annual_production_mwh = assumptions.production_mwh_year1
        capacity_factor = annual_production_mwh / (8760 * capacity_mw)
        project_life = assumptions.project_life_years
        discount_rate = assumptions.discount_rate
        
        # Raw CAPEX (without grants)
        raw_capex_per_kw = capex_total / (capacity_mw * 1000)  # EUR/kW
        
        # OPEX components (without incentives)
        opex_per_kw_year = (assumptions.opex_meur * 1e6) / (capacity_mw * 1000)  # EUR/kW/year
        insurance_cost_per_kw_year = (assumptions.insurance_rate * capex_total) / (capacity_mw * 1000)
        land_lease_per_kw_year = (assumptions.land_lease_meur * 1e6) / (capacity_mw * 1000)
        
        # Total fixed O&M (without incentives)
        total_fixed_om_per_kw_year = opex_per_kw_year + insurance_cost_per_kw_year + land_lease_per_kw_year
        
        # Capital Recovery Factor (CRF)
        crf = (discount_rate * (1 + discount_rate)**project_life) / ((1 + discount_rate)**project_life - 1)
        
        # Capital component
        capital_component = (raw_capex_per_kw * crf) / (8760 * capacity_factor)
        
        # Fixed O&M component
        fixed_om_component = total_fixed_om_per_kw_year / (8760 * capacity_factor)
        
        # Variable O&M (minimal for solar)
        variable_om_component = 0.002  # EUR/kWh for solar maintenance
        
        # Corporate tax impact (full 31% rate, no holiday)
        tax_rate = 0.31
        tax_adjustment = 1 / (1 - tax_rate)  # Adjust for tax burden
        
        # Raw LCOE (pre-tax)
        raw_lcoe_pretax = capital_component + fixed_om_component + variable_om_component
        
        # Raw LCOE (after-tax, without incentives)
        raw_lcoe = raw_lcoe_pretax * tax_adjustment
        raw_lcoe = max(raw_lcoe, 0.020)  # Minimum floor of 2 cents/kWh
        
        print(f"📊 Raw LCOE (No Incentives): {raw_lcoe:.4f} EUR/kWh")
        
        # Calculate total reduction
        total_reduction = raw_lcoe - enhanced_lcoe
        reduction_percentage = (total_reduction / raw_lcoe) * 100
        
        print(f"📊 Total LCOE Reduction: {total_reduction:.4f} EUR/kWh ({reduction_percentage:.1f}%)")
        
        # Test waterfall chart data consistency
        print("\n🎯 Testing Waterfall Chart Data...")
        
        # Generate waterfall chart to test data consistency
        fig = generate_lcoe_waterfall_chart(assumptions, kpis, output_path=None)
        
        if fig:
            print("✅ Waterfall chart generated successfully")
            plt.show()
        else:
            print("❌ Waterfall chart generation failed")
        
        # Verify consistency
        print("\n🔍 LCOE Consistency Check:")
        print(f"   Enhanced Model LCOE: {enhanced_lcoe:.4f} EUR/kWh")
        print(f"   Raw LCOE: {raw_lcoe:.4f} EUR/kWh")
        print(f"   Expected Final LCOE: {raw_lcoe - total_reduction:.4f} EUR/kWh")
        
        if abs(enhanced_lcoe - (raw_lcoe - total_reduction)) < 0.001:
            print("✅ LCOE calculations are SYNCHRONIZED")
        else:
            print("❌ LCOE calculations are NOT synchronized")
            
        return {
            'enhanced_lcoe': enhanced_lcoe,
            'raw_lcoe': raw_lcoe,
            'total_reduction': total_reduction,
            'reduction_percentage': reduction_percentage
        }
        
    except Exception as e:
        print(f"❌ Error in LCOE calculation: {e}")
        return None

if __name__ == "__main__":
    results = test_lcoe_synchronization()
    
    if results:
        print(f"\n📋 SUMMARY:")
        print(f"   Raw LCOE: {results['raw_lcoe']:.4f} EUR/kWh")
        print(f"   Enhanced LCOE: {results['enhanced_lcoe']:.4f} EUR/kWh") 
        print(f"   Total Reduction: {results['total_reduction']:.4f} EUR/kWh")
        print(f"   Savings: {results['reduction_percentage']:.1f}%")
