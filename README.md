# ⚡ Renewable Energy Financial Modeling Suite

Advanced financial analysis application for solar, wind, and hybrid renewable energy projects in Morocco.

## 🌟 Features

### Multi-Technology Support
- **Solar Energy Projects** 🌞: Traditional photovoltaic installations
- **Wind Energy Projects** 💨: Wind farm developments
- **Hybrid Systems** 🔄: Combined solar and wind installations

### Key Capabilities
- Financial modeling with IRR, NPV, DSCR calculations
- Sensitivity analysis for risk assessment
- Interactive charts and visualizations
- Export functionality to Excel
- Pre-configured Moroccan location profiles
- Grant and incentive modeling (Italian, MASEN, IRESEN)

## 🚀 Quick Start

### Running from Source
```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
python main.py
```

### Using the Executable
1. Download `RenewableEnergyFinancialSuite.exe` from the `dist` folder
2. Double-click to launch (no console window will appear)
3. The application will open in your default web browser

## 💡 Usage Guide

### Getting Started
1. **Select Location**: Choose from pre-configured Moroccan renewable energy sites
2. **Choose Technology**: Select Solar, Wind, or Hybrid system type
3. **Configure Parameters**: Adjust financial parameters as needed
4. **Run Analysis**: Click "Run Financial Model" to generate results

### Technology-Specific Guidelines

#### Solar Projects 🌞
- Higher CAPEX (8-10 M€ for 5MW)
- Lower OPEX (200-300 k€/year)
- 0.4% annual degradation
- PPA Price: 0.04-0.08 €/kWh

#### Wind Projects 💨
- Lower CAPEX (7-9 M€ for 5MW)
- Higher OPEX (250-350 k€/year)
- 0.2% annual degradation
- PPA Price: 0.03-0.07 €/kWh

#### Hybrid Systems 🔄
- Balanced CAPEX/OPEX
- 0.3% annual degradation
- Higher capacity factors
- Optimized for grid stability

## 📊 Available Charts

- Cumulative Free Cash Flow to Equity (FCFE)
- Debt Service Coverage Ratio (DSCR)
- Sensitivity Analysis
- Financing Structure
- Scenario Comparison
- Project Development Timeline (Gantt)

## 🏗️ Technical Details

### Built With
- **Flet**: Modern Python UI framework
- **Matplotlib/Seaborn**: Data visualization
- **Pandas/NumPy**: Data processing
- **PyInstaller**: Executable generation

### Project Structure
```
├── main.py                 # Main application entry point
├── core/
│   ├── data_models.py      # Data structures and models
│   ├── financial_model_logic.py  # Financial calculations
│   └── figure_generator_logic.py # Chart generation
├── requirements.txt        # Python dependencies
└── RenewableEnergyFinancialSuite.spec  # PyInstaller configuration
```

## 🔧 Development

### Building the Executable
```bash
# Clean build
pyinstaller --clean RenewableEnergyFinancialSuite.spec
```

### Key Enhancements Made
1. **Multi-Technology Support**: Added technology type selection (solar/wind/hybrid)
2. **Dynamic Parameters**: Technology-specific defaults and validation
3. **Enhanced UI**: Updated branding and user guidance
4. **Improved Profiles**: Location-based technology recommendations
5. **Console-Free Executable**: No command window on startup

## 📝 License

Proprietary software developed by Abdelhalim Serhani for Agevolami.
Unauthorized copying, distribution, or modification is prohibited.

## 📧 Contact

**Created by**: Abdelhalim Serhani  
**Company**: Agevolami - Business & Financial Consulting  
**Email**: <EMAIL>  
**Alternative**: <EMAIL>

---

*Designed specifically for Italian businesses investing in Moroccan renewable energy projects.*