"""
Creative Security Screen for Enhanced Financial Model
Author: <PERSON><PERSON><PERSON><PERSON> - Financial & Business Consultant at Agevolami.it/ma
"""

import flet as ft
import hashlib
from typing import Callable, Optional
from .config import SecurityConfig


class SecurityManager:
    """Manages authentication and security screen display"""
    
    def __init__(self, page: ft.Page):
        self.page = page
        self.page.title = "Financial Model - Security Access"
        self.page.theme_mode = ft.ThemeMode.LIGHT
        self.page.window_width = 1000
        self.page.window_height = 700
        self.page.scroll = ft.ScrollMode.AUTO
        self.page.vertical_alignment = ft.MainAxisAlignment.CENTER
        self.page.horizontal_alignment = ft.CrossAxisAlignment.CENTER
        
        # Authentication state
        self.is_authenticated = False
        self.on_success_callback: Optional[Callable] = None
        
        # UI Components
        self.password_field = None
        self.error_text = None
        self.login_button = None
        
    def show_security_screen(self, on_success: Callable):
        """Display the creative security screen"""
        self.on_success_callback = on_success
        self._build_security_ui()
        
    def _build_security_ui(self):
        """Build the creative security interface"""
        
        # Main container with gradient-like effect
        main_container = ft.Container(
            content=ft.Column([
                # Header section with branding
                self._create_header_section(),
                
                # Developer showcase section
                self._create_developer_section(),
                
                # Authentication section
                self._create_auth_section(),
                
                # Footer with additional info
                self._create_footer_section()
                
            ], 
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=30),
            
            width=800,
            padding=40,
            bgcolor=ft.Colors.WHITE,
            border_radius=20,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=15,
                color=ft.Colors.with_opacity(0.3, ft.Colors.BLUE_GREY_300),
                offset=ft.Offset(0, 5)
            )
        )
        
        # Background container
        background = ft.Container(
            content=main_container,
            alignment=ft.alignment.center,
            gradient=ft.LinearGradient(
                begin=ft.alignment.top_left,
                end=ft.alignment.bottom_right,
                colors=[
                    ft.Colors.BLUE_50,
                    ft.Colors.INDIGO_50,
                    ft.Colors.PURPLE_50
                ]
            ),
            expand=True
        )
        
        self.page.add(background)
        self.page.update()
        
    def _create_header_section(self) -> ft.Container:
        """Create the header section with app branding"""
        return ft.Container(
            content=ft.Column([
                # App icon/logo placeholder
                ft.Container(
                    content=ft.Icon(
                        ft.Icons.SECURITY,
                        size=60,
                        color=ft.Colors.BLUE_600
                    ),
                    width=100,
                    height=100,
                    bgcolor=ft.Colors.BLUE_50,
                    border_radius=50,
                    alignment=ft.alignment.center
                ),
                
                # App title
                ft.Text(
                    "Enhanced Financial Model",
                    size=32,
                    weight=ft.FontWeight.BOLD,
                    color=ft.Colors.BLUE_900,
                    text_align=ft.TextAlign.CENTER
                ),
                
                ft.Text(
                    "Professional Edition - Secure Access",
                    size=16,
                    color=ft.Colors.BLUE_700,
                    text_align=ft.TextAlign.CENTER
                )
            ],
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=15),
            
            alignment=ft.alignment.center
        )
        
    def _create_developer_section(self) -> ft.Container:
        """Create the developer showcase section"""
        dev_info = SecurityConfig.get_developer_info()

        return ft.Container(
            content=ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        # Developer header
                        ft.Row([
                            ft.Icon(ft.Icons.PERSON, color=ft.Colors.ORANGE_600, size=30),
                            ft.Text(
                                "Developed by",
                                size=18,
                                weight=ft.FontWeight.BOLD,
                                color=ft.Colors.GREY_800
                            )
                        ], alignment=ft.MainAxisAlignment.CENTER),

                        # Developer name and title
                        ft.Text(
                            dev_info['name'],
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.BLUE_900,
                            text_align=ft.TextAlign.CENTER
                        ),

                        ft.Text(
                            dev_info['title'],
                            size=16,
                            color=ft.Colors.GREY_700,
                            text_align=ft.TextAlign.CENTER
                        ),

                        # Company branding
                        ft.Container(
                            content=ft.Row([
                                ft.Icon(ft.Icons.BUSINESS, color=ft.Colors.GREEN_600, size=20),
                                ft.Text(
                                    dev_info['company'],
                                    size=18,
                                    weight=ft.FontWeight.BOLD,
                                    color=ft.Colors.GREEN_700
                                )
                            ], alignment=ft.MainAxisAlignment.CENTER),

                            bgcolor=ft.Colors.GREEN_50,
                            padding=10,
                            border_radius=10,
                            border=ft.border.all(1, ft.Colors.GREEN_200)
                        ),

                        # Specialization tags
                        ft.Row([
                            self._create_tag(spec, [ft.Colors.BLUE_600, ft.Colors.PURPLE_600, ft.Colors.ORANGE_600][i])
                            for i, spec in enumerate(dev_info['specializations'])
                        ], alignment=ft.MainAxisAlignment.CENTER, wrap=True)

                    ],
                    alignment=ft.MainAxisAlignment.CENTER,
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=15),

                    padding=25
                ),
                elevation=3
            ),
            width=600
        )
        
    def _create_tag(self, text: str, color: str) -> ft.Container:
        """Create a styled tag"""
        return ft.Container(
            content=ft.Text(
                text,
                size=12,
                color=ft.Colors.WHITE,
                weight=ft.FontWeight.BOLD
            ),
            bgcolor=color,
            padding=ft.padding.symmetric(horizontal=12, vertical=6),
            border_radius=15,
            margin=ft.margin.all(3)
        )
        
    def _create_auth_section(self) -> ft.Container:
        """Create the authentication section"""
        # Password field
        self.password_field = ft.TextField(
            label="Access Password",
            hint_text="Enter your access password",
            password=True,
            can_reveal_password=True,
            width=400,
            prefix_icon=ft.Icons.LOCK,
            border_color=ft.Colors.BLUE_300,
            focused_border_color=ft.Colors.BLUE_600,
            on_submit=self._handle_login
        )
        
        # Error text
        self.error_text = ft.Text(
            "",
            color=ft.Colors.RED_600,
            size=14,
            visible=False
        )
        
        # Login button
        self.login_button = ft.ElevatedButton(
            text="🔓 Access Application",
            icon=ft.Icons.LOGIN,
            on_click=self._handle_login,
            bgcolor=ft.Colors.BLUE_600,
            color=ft.Colors.WHITE,
            width=400,
            height=50
        )
        
        return ft.Container(
            content=ft.Card(
                content=ft.Container(
                    content=ft.Column([
                        ft.Text(
                            "🔐 Secure Access Required",
                            size=20,
                            weight=ft.FontWeight.BOLD,
                            color=ft.Colors.GREY_800,
                            text_align=ft.TextAlign.CENTER
                        ),
                        
                        self.password_field,
                        self.error_text,
                        self.login_button,
                        
                        ft.Text(
                            "This application contains sensitive financial models and data.",
                            size=12,
                            color=ft.Colors.GREY_600,
                            text_align=ft.TextAlign.CENTER,
                            italic=True
                        )
                        
                    ],
                    alignment=ft.MainAxisAlignment.CENTER,
                    horizontal_alignment=ft.CrossAxisAlignment.CENTER,
                    spacing=20),
                    
                    padding=30
                ),
                elevation=2
            ),
            width=500
        )
        
    def _create_footer_section(self) -> ft.Container:
        """Create the footer section"""
        dev_info = SecurityConfig.get_developer_info()

        return ft.Container(
            content=ft.Column([
                ft.Divider(color=ft.Colors.GREY_300),

                ft.Row([
                    ft.Icon(ft.Icons.COPYRIGHT, size=16, color=ft.Colors.GREY_600),
                    ft.Text(
                        f"{dev_info['year']} {dev_info['company']} - Professional Financial Consulting",
                        size=12,
                        color=ft.Colors.GREY_600
                    )
                ], alignment=ft.MainAxisAlignment.CENTER),

                ft.Text(
                    "Specialized in Italian-Moroccan Renewable Energy Projects",
                    size=11,
                    color=ft.Colors.GREY_500,
                    text_align=ft.TextAlign.CENTER,
                    italic=True
                )
            ],
            spacing=5),

            width=600
        )
        
    def _handle_login(self, e):
        """Handle login attempt"""
        password = self.password_field.value
        
        if not password:
            self._show_error("Please enter a password")
            return
            
        if self._verify_password(password):
            self._show_success()
        else:
            self._show_error("Invalid password. Please try again.")
            
    def _verify_password(self, password: str) -> bool:
        """Verify the entered password"""
        # Get password from configuration
        configured_password = SecurityConfig.get_password()
        return password == configured_password
        
    def _show_error(self, message: str):
        """Show error message"""
        self.error_text.value = f"❌ {message}"
        self.error_text.visible = True
        self.password_field.border_color = ft.Colors.RED_400
        self.password_field.focused_border_color = ft.Colors.RED_600
        self.page.update()

        # Clear password field for security
        self.password_field.value = ""
        self.password_field.focus()

    def _show_success(self):
        """Handle successful authentication"""
        self.is_authenticated = True

        # Show success message briefly
        self.error_text.value = "✅ Access granted! Loading application..."
        self.error_text.color = ft.Colors.GREEN_600
        self.error_text.visible = True
        self.login_button.disabled = True
        self.password_field.disabled = True
        self.page.update()

        # Call success callback immediately
        if self.on_success_callback:
            self.on_success_callback()
