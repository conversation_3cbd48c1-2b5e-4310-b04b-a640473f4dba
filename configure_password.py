"""
Password Configuration Utility for Enhanced Financial Model
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from auth.config import SecurityConfig

def main():
    """Configure the application password"""
    print("🔐 Enhanced Financial Model - Password Configuration")
    print("=" * 50)
    
    # Show current password
    current_password = SecurityConfig.get_password()
    print(f"Current password: {current_password}")
    print()
    
    # Get new password
    while True:
        new_password = input("Enter new password (or 'quit' to exit): ").strip()
        
        if new_password.lower() == 'quit':
            print("Configuration cancelled.")
            return
            
        if len(new_password) < 6:
            print("❌ Password must be at least 6 characters long.")
            continue
            
        # Confirm password
        confirm_password = input("Confirm new password: ").strip()
        
        if new_password != confirm_password:
            print("❌ Passwords do not match. Please try again.")
            continue
            
        # Set new password
        if SecurityConfig.set_password(new_password):
            print(f"✅ Password successfully updated to: {new_password}")
            print()
            print("The new password will be used the next time you start the application.")
            break
        else:
            print("❌ Failed to save password configuration.")
            break

if __name__ == "__main__":
    main()
