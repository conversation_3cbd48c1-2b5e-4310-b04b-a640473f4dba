#!/usr/bin/env python3
"""
Test DOCX export from the main application
"""

import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_main_app_docx():
    """Test DOCX export functionality from the main app"""
    print("🔬 Testing Main App DOCX Export")
    print("=" * 40)
    
    try:
        # Import the main app components
        print("1. Importing main app components...")
        from enhanced_main import EnhancedFinancialModelApp
        from core.enhanced_data_models import EnhancedProjectAssumptions
        import flet as ft
        print("   ✅ Imports successful")
        
        # Create a mock page for testing
        print("2. Creating test environment...")
        
        class MockPage:
            def __init__(self):
                pass
            def update(self):
                pass
        
        mock_page = MockPage()
        
        # Create app instance
        app = EnhancedFinancialModelApp(mock_page)
        print("   ✅ App instance created")
        
        # Set up test assumptions
        print("3. Setting up test data...")
        app.assumptions = EnhancedProjectAssumptions(
            location_name="Test Morocco Project",
            capacity_mw=10.0,
            capex_meur=8.5,
            production_mwh_year1=22000,
            ppa_price_eur_kwh=0.055,
            years=25,
            debt_ratio=0.7,
            interest_rate=0.05,
            grant_meur_italy=1.2,
            grant_meur_simest=0.5,
            grant_meur_masen=0.8,
            grant_meur_connection=0.3
        )
        
        # Create mock results
        app.current_results = {
            'kpis': {
                'IRR_project': 0.152,
                'IRR_equity': 0.185,
                'LCOE_eur_kwh': 0.051,
                'NPV_project': 7300000,
                'NPV_equity': 4100000,
                'Min_DSCR': 1.25,
                'Payback_years': 8.5
            },
            'cashflow': None,  # Not needed for DOCX test
            'assumptions': {}
        }
        print("   ✅ Test data set up")
        
        # Test DOCX export
        print("4. Testing DOCX export...")
        
        # Create a mock event
        class MockEvent:
            pass
        
        mock_event = MockEvent()
        
        # Call the export function
        app.export_docx_report(mock_event)
        print("   ✅ DOCX export function called")
        
        # Check if any DOCX files were created
        print("5. Checking for created files...")
        docx_files = list(Path('.').glob('financial_report_*.docx'))
        
        if docx_files:
            latest_file = max(docx_files, key=lambda f: f.stat().st_mtime)
            file_size = latest_file.stat().st_size
            print(f"   ✅ DOCX file found: {latest_file}")
            print(f"   📁 Size: {file_size} bytes")
            print(f"   📍 Location: {latest_file.absolute()}")
            return True
        else:
            print("   ❌ No DOCX files found")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_existing_docx_files():
    """Check for existing DOCX files"""
    print("\n📁 Checking for existing DOCX files...")
    
    docx_files = list(Path('.').glob('*.docx'))
    
    if docx_files:
        print(f"Found {len(docx_files)} DOCX file(s):")
        for file in docx_files:
            size = file.stat().st_size
            print(f"  📄 {file.name} ({size} bytes)")
    else:
        print("  No DOCX files found in current directory")

if __name__ == "__main__":
    # Check existing files first
    check_existing_docx_files()
    
    # Run the test
    success = test_main_app_docx()
    
    if success:
        print("\n🎉 Main App DOCX Export is working!")
        print("✅ The DOCX export function in the main app works correctly")
    else:
        print("\n🚨 Main App DOCX Export has issues")
        print("❌ Check the error messages above")
    
    # Check files again
    check_existing_docx_files()
