# DOCX Export Troubleshooting Guide

## 🎯 Issue: DOCX Export Not Working

If the DOCX export function is not working in the Enhanced Financial Model app, follow this troubleshooting guide.

## 🔍 Diagnosis

### Step 1: Check if python-docx is installed

Run this command in your terminal:

```bash
python -c "import docx; print('✅ python-docx is available')"
```

**Expected Results:**
- ✅ **Success**: `✅ python-docx is available`
- ❌ **Failure**: `ModuleNotFoundError: No module named 'docx'`

### Step 2: Test DOCX functionality independently

Run the standalone test utility:

```bash
python docx_export_utility.py
```

This will:
- Check python-docx availability
- Create a sample DOCX report
- Provide detailed error messages if issues exist

## 🔧 Solutions

### Solution 1: Install python-docx

Choose the appropriate command for your environment:

#### For pip users:
```bash
pip install python-docx
```

#### For conda users:
```bash
conda install python-docx
```

#### For pip3 users:
```bash
pip3 install python-docx
```

#### Install all requirements at once:
```bash
pip install -r requirements_enhanced.txt
```

### Solution 2: Verify Installation

After installation, verify it works:

```bash
python -c "from docx import Document; print('✅ Installation successful')"
```

### Solution 3: Restart the Application

After installing python-docx:
1. Close the Enhanced Financial Model app completely
2. Restart the application
3. The DOCX export button should now be enabled

## 🚨 Common Issues and Fixes

### Issue 1: "DOCX Export button is disabled"

**Cause**: python-docx not installed or not detected

**Fix**: 
1. Install python-docx using commands above
2. Restart the application
3. Button should become enabled

### Issue 2: "ImportError: No module named 'docx'"

**Cause**: python-docx not installed

**Fix**: Install using `pip install python-docx`

### Issue 3: "Permission denied when saving DOCX file"

**Cause**: File permissions or file already open

**Fix**: 
1. Close any open DOCX files with the same name
2. Check file permissions in the directory
3. Try running as administrator (Windows) or with sudo (Linux/Mac)

### Issue 4: "DOCX file created but appears corrupted"

**Cause**: Incomplete installation or version conflict

**Fix**: 
1. Uninstall and reinstall python-docx:
   ```bash
   pip uninstall python-docx
   pip install python-docx
   ```
2. Restart the application

### Issue 5: "Module 'docx' has no attribute 'Document'"

**Cause**: Wrong package installed (might have installed 'docx' instead of 'python-docx')

**Fix**: 
1. Uninstall the wrong package:
   ```bash
   pip uninstall docx
   ```
2. Install the correct package:
   ```bash
   pip install python-docx
   ```

## 🧪 Testing the Fix

### Test 1: Run the standalone utility
```bash
python docx_export_utility.py
```

### Test 2: Test in the main application
1. Open the Enhanced Financial Model app
2. Run a financial model calculation
3. Click "Export DOCX Report"
4. Check if the file is created successfully

### Test 3: Verify DOCX file content
1. Open the generated DOCX file in Microsoft Word or LibreOffice
2. Verify it contains:
   - Executive Summary
   - Key Performance Indicators table
   - Financing Structure table
   - Grant Breakdown
   - Financial Analysis text
   - Risk Factors
   - Recommendations

## 📋 Expected DOCX Report Structure

A successful DOCX export should contain:

```
Enhanced Financial Model Report
├── Executive Summary
│   └── Project details table
├── Key Performance Indicators
│   └── KPIs table (IRR, LCOE, NPV, etc.)
├── Financing Structure
│   └── Funding sources table
├── Grant Breakdown
│   └── Individual grant amounts
├── Financial Analysis
│   └── Narrative analysis with insights
├── Key Risk Factors
│   └── Project risks list
└── Recommendations
    └── Strategic recommendations
```

## 🔄 Alternative Solutions

### Option 1: Manual Installation Check

Create a simple test file `test_docx.py`:

```python
try:
    from docx import Document
    doc = Document()
    doc.add_heading('Test', 0)
    doc.save('test.docx')
    print("✅ DOCX functionality working")
except Exception as e:
    print(f"❌ Error: {e}")
```

Run: `python test_docx.py`

### Option 2: Use Virtual Environment

If you have conflicts with other packages:

```bash
# Create virtual environment
python -m venv docx_env

# Activate it
# Windows:
docx_env\Scripts\activate
# Linux/Mac:
source docx_env/bin/activate

# Install requirements
pip install python-docx flet numpy pandas matplotlib

# Run the application
python enhanced_main.py
```

### Option 3: Check Python Version Compatibility

python-docx requires Python 3.6+. Check your version:

```bash
python --version
```

If using an older version, upgrade Python or use a compatible version of python-docx.

## 📞 Getting Help

If the issue persists:

1. **Check the error message** in the app status bar
2. **Run the diagnostic utility**: `python docx_export_utility.py`
3. **Verify your Python environment** and package versions
4. **Try the manual installation steps** above

## ✅ Success Indicators

You'll know DOCX export is working when:

- ✅ The "Export DOCX Report" button is enabled (not grayed out)
- ✅ Clicking the button shows "✅ DOCX report exported: filename.docx"
- ✅ A DOCX file is created in the application directory
- ✅ The DOCX file opens correctly in Word/LibreOffice
- ✅ The file contains all expected sections and data

---

*Last updated: December 2024*
*For technical support, refer to the Enhanced Financial Model documentation*
