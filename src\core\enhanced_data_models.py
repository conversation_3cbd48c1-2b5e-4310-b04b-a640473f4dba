from __future__ import annotations

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple
import json
from pathlib import Path

@dataclass
class EnhancedProjectAssumptions:
    """Enhanced project assumptions with industry best practices"""
    
    # Basic project information
    location_name: str = "Ouarzazate"
    capacity_mw: float = 10.0
    project_life_years: int = 25
    
    # Financial structure
    capex_meur: float = 8.5
    debt_ratio: float = 0.70
    interest_rate: float = 0.055
    debt_years: int = 18
    grace_years: int = 2
    discount_rate: float = 0.10  # WACC
    
    # Revenue parameters
    production_mwh_year1: float = 26_000
    ppa_price_eur_kwh: float = 0.045
    price_escalation: float = 0.02
    
    # Operational parameters
    opex_keuros_year1: float = 180
    opex_escalation: float = 0.025
    insurance_rate: float = 0.003  # % of CAPEX annually
    land_lease_eur_mw_year: float = 2_000
    
    # Performance degradation
    degradation_year1: float = 0.025  # First year degradation
    degradation_annual: float = 0.005  # Annual degradation after year 1
    
    # Grant structure (corrected for commercial projects)
    grant_meur_italy: float = 1.2  # Italian government support
    grant_meur_masen: float = 0.8   # MASEN strategic project support
    grant_meur_iresen: float = 0.0  # Research grants (not for commercial)
    grant_meur_connection: float = 0.3  # Grid connection subsidies
    
    # Tax parameters
    corporate_tax_rate: float = 0.31  # Morocco corporate tax
    withholding_tax_rate: float = 0.10  # Dividend withholding tax
    tax_holiday_years: int = 5
    
    # Working capital
    working_capital_days: int = 30
    
    # Terminal value
    use_terminal_value: bool = True
    terminal_method: str = "perpetuity"  # "perpetuity" or "exit_multiple"
    terminal_growth_rate: float = 0.025  # 2.5% perpetual growth
    exit_multiple_ebitda: float = 8.0    # EV/EBITDA exit multiple
    
    # Risk parameters for Monte Carlo
    production_volatility: float = 0.05
    price_volatility: float = 0.03
    capex_volatility: float = 0.10
    opex_volatility: float = 0.05
    
    # Calculated fields
    total_grant_meur_maroc: float = field(init=False)
    total_grants_meur: float = field(init=False)
    investment_for_debt_sizing_meur: float = field(init=False)
    
    def __post_init__(self):
        """Calculate dependent fields"""
        self.total_grant_meur_maroc = (
            self.grant_meur_masen + 
            self.grant_meur_connection
            # Removed IRESEN for commercial projects
        )
        self.total_grants_meur = (
            self.grant_meur_italy + 
            self.total_grant_meur_maroc
        )
        self.investment_for_debt_sizing_meur = (
            self.capex_meur - self.total_grants_meur
        )
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        return {
            k: v for k, v in self.__dict__.items() 
            if not k.startswith('_')
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'EnhancedProjectAssumptions':
        """Create instance from dictionary"""
        return cls(**{k: v for k, v in data.items() if k in cls.__dataclass_fields__})
    
    def save_to_file(self, filepath: str) -> None:
        """Save assumptions to JSON file"""
        with open(filepath, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load_from_file(cls, filepath: str) -> 'EnhancedProjectAssumptions':
        """Load assumptions from JSON file"""
        with open(filepath, 'r') as f:
            data = json.load(f)
        return cls.from_dict(data)

@dataclass
class MarketAssumptions:
    """Market-specific assumptions for different regions"""
    
    # Market parameters
    country: str = "Morocco"
    currency: str = "EUR"
    inflation_rate: float = 0.025
    
    # Regulatory environment
    renewable_target_pct: float = 52.0  # Morocco's 2030 target
    feed_in_tariff_available: bool = False
    net_metering_available: bool = True
    
    # Grid and infrastructure
    grid_connection_cost_eur_mw: float = 50_000
    transmission_losses_pct: float = 0.08
    
    # Market risks
    political_risk_premium: float = 0.02
    currency_risk_premium: float = 0.015
    
    # Benchmarks
    market_lcoe_eur_kwh: float = 0.042  # Market benchmark LCOE
    market_irr_target: float = 0.12     # Target IRR for the market

@dataclass
class TechnicalAssumptions:
    """Technical assumptions for solar PV projects"""
    
    # Technology parameters
    technology_type: str = "Solar PV"
    module_efficiency: float = 0.21  # 21% efficiency
    inverter_efficiency: float = 0.98
    system_losses_pct: float = 0.15  # Total system losses
    
    # Performance parameters
    capacity_factor_year1: float = 0.297  # ~26,000 MWh / (10 MW * 8760 h)
    irradiation_kwh_m2_year: float = 2_100  # Ouarzazate solar irradiation
    
    # O&M parameters
    availability_target: float = 0.98
    maintenance_cycles_per_year: int = 4
    
    # Equipment life
    module_warranty_years: int = 25
    inverter_replacement_year: int = 12
    inverter_replacement_cost_eur_mw: float = 80_000

@dataclass
class FinancingAssumptions:
    """Detailed financing structure assumptions"""
    
    # Debt structure
    senior_debt_ratio: float = 0.70
    subordinated_debt_ratio: float = 0.0
    
    # Interest rates
    senior_debt_rate: float = 0.055
    subordinated_debt_rate: float = 0.08
    
    # Fees and costs
    arrangement_fee_pct: float = 0.02  # % of debt amount
    commitment_fee_pct: float = 0.005  # % of undrawn debt
    
    # Covenants
    min_dscr_covenant: float = 1.20
    max_debt_to_equity: float = 2.33  # 70/30 debt/equity
    
    # Equity structure
    sponsor_equity_pct: float = 0.80
    financial_investor_equity_pct: float = 0.20
    
    # Returns
    sponsor_target_irr: float = 0.15
    financial_investor_target_irr: float = 0.12

@dataclass
class RiskAssumptions:
    """Risk parameters for sensitivity and Monte Carlo analysis"""
    
    # Production risks
    weather_volatility: float = 0.05  # Annual weather variation
    equipment_failure_probability: float = 0.02
    
    # Market risks
    ppa_counterparty_risk: str = "Low"  # Low, Medium, High
    merchant_price_volatility: float = 0.15
    
    # Regulatory risks
    policy_change_probability: float = 0.10
    tariff_change_risk: float = 0.05
    
    # Construction risks
    cost_overrun_probability: float = 0.15
    delay_probability: float = 0.10
    
    # Operational risks
    force_majeure_probability: float = 0.01
    insurance_coverage_pct: float = 0.95

@dataclass
class ScenarioDefinition:
    """Definition for scenario analysis"""
    
    name: str
    description: str
    
    # Parameter adjustments (multipliers)
    production_multiplier: float = 1.0
    price_multiplier: float = 1.0
    capex_multiplier: float = 1.0
    opex_multiplier: float = 1.0
    discount_rate_adjustment: float = 0.0  # Absolute adjustment
    
    # Grant adjustments
    italy_grant_multiplier: float = 1.0
    morocco_grant_multiplier: float = 1.0
    
    def apply_to_assumptions(self, base_assumptions: EnhancedProjectAssumptions) -> EnhancedProjectAssumptions:
        """Apply scenario adjustments to base assumptions"""
        # Get dict and exclude calculated fields
        base_dict = base_assumptions.to_dict()
        init_fields = {k: v for k, v in base_dict.items() 
                      if k in EnhancedProjectAssumptions.__dataclass_fields__ 
                      and EnhancedProjectAssumptions.__dataclass_fields__[k].init}
        adjusted = EnhancedProjectAssumptions(**init_fields)
        
        # Apply multipliers
        adjusted.production_mwh_year1 *= self.production_multiplier
        adjusted.ppa_price_eur_kwh *= self.price_multiplier
        adjusted.capex_meur *= self.capex_multiplier
        adjusted.opex_keuros_year1 *= self.opex_multiplier
        
        # Apply discount rate adjustment
        adjusted.discount_rate += self.discount_rate_adjustment
        
        # Apply grant adjustments
        adjusted.grant_meur_italy *= self.italy_grant_multiplier
        adjusted.grant_meur_masen *= self.morocco_grant_multiplier
        adjusted.grant_meur_connection *= self.morocco_grant_multiplier
        
        # Update name (properties are automatically calculated)
        adjusted.location_name = self.name
        
        return adjusted

# Predefined scenarios
PREDEFINED_SCENARIOS = {
    "base": ScenarioDefinition(
        name="Base Case",
        description="Base case with current assumptions"
    ),
    
    "conservative": ScenarioDefinition(
        name="Conservative",
        description="Conservative scenario with higher costs and lower performance",
        production_multiplier=0.95,
        capex_multiplier=1.15,
        opex_multiplier=1.10,
        discount_rate_adjustment=0.02
    ),
    
    "optimistic": ScenarioDefinition(
        name="Optimistic",
        description="Optimistic scenario with lower costs and higher performance",
        production_multiplier=1.05,
        price_multiplier=1.05,
        capex_multiplier=0.90,
        opex_multiplier=0.95,
        discount_rate_adjustment=-0.02
    ),
    
    "stress": ScenarioDefinition(
        name="Stress Test",
        description="Stress scenario with adverse conditions",
        production_multiplier=0.85,
        price_multiplier=0.90,
        capex_multiplier=1.25,
        opex_multiplier=1.20,
        discount_rate_adjustment=0.03
    ),
    
    "no_grants": ScenarioDefinition(
        name="No Grants",
        description="Scenario without any government grants",
        italy_grant_multiplier=0.0,
        morocco_grant_multiplier=0.0
    ),
    
    "high_irradiation": ScenarioDefinition(
        name="High Irradiation",
        description="Scenario with higher solar irradiation (Dakhla-like conditions)",
        production_multiplier=1.15
    )
}

@dataclass
class ModelValidation:
    """Validation parameters and benchmarks"""
    
    # Industry benchmarks
    benchmark_lcoe_range: Tuple[float, float] = (0.035, 0.055)  # EUR/kWh
    benchmark_irr_range: Tuple[float, float] = (0.08, 0.18)     # Equity IRR
    benchmark_dscr_min: float = 1.20
    
    # Model validation flags
    validate_against_benchmarks: bool = True
    require_positive_npv: bool = True
    require_min_irr: float = 0.08
    
    # Data quality checks
    max_capex_eur_mw: float = 1_200_000  # Maximum reasonable CAPEX
    min_capacity_factor: float = 0.15     # Minimum reasonable CF
    max_capacity_factor: float = 0.35     # Maximum reasonable CF
    
    def validate_assumptions(self, assumptions: EnhancedProjectAssumptions) -> List[str]:
        """Validate assumptions against benchmarks and return warnings"""
        warnings = []
        
        # CAPEX validation
        capex_per_mw = assumptions.capex_meur * 1_000_000 / assumptions.capacity_mw
        if capex_per_mw > self.max_capex_eur_mw:
            warnings.append(f"CAPEX per MW ({capex_per_mw:,.0f} EUR/MW) exceeds benchmark ({self.max_capex_eur_mw:,.0f} EUR/MW)")
        
        # Capacity factor validation
        cf = assumptions.production_mwh_year1 / (assumptions.capacity_mw * 8760)
        if cf < self.min_capacity_factor or cf > self.max_capacity_factor:
            warnings.append(f"Capacity factor ({cf:.1%}) outside reasonable range ({self.min_capacity_factor:.1%} - {self.max_capacity_factor:.1%})")
        
        # Debt ratio validation
        if assumptions.debt_ratio > 0.80:
            warnings.append(f"Debt ratio ({assumptions.debt_ratio:.1%}) is very high for renewable projects")
        
        # Interest rate validation
        if assumptions.interest_rate > 0.10:
            warnings.append(f"Interest rate ({assumptions.interest_rate:.1%}) seems high for current market conditions")
        
        return warnings

# Default model configuration
DEFAULT_MODEL_CONFIG = {
    "project": EnhancedProjectAssumptions(),
    "market": MarketAssumptions(),
    "technical": TechnicalAssumptions(),
    "financing": FinancingAssumptions(),
    "risk": RiskAssumptions(),
    "validation": ModelValidation()
}