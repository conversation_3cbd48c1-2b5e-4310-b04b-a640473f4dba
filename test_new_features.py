#!/usr/bin/env python3
"""
Test script for the new directory structure and comprehensive report features
"""

import sys
from pathlib import Path

# Add src to path
sys.path.append('src')

def test_directory_structure():
    """Test the new timestamped directory structure"""
    try:
        from enhanced_main import EnhancedFinancialModelApp
        from datetime import datetime
        
        # Mock page class
        class MockPage:
            def __init__(self):
                self.title = ''
                self.theme_mode = None
                self.window_width = 1400
                self.window_height = 900
                self.scroll = None
            def add(self, content):
                pass
            def update(self):
                pass

        # Create a mock app instance
        page = MockPage()
        app = EnhancedFinancialModelApp(page)

        # Test the timestamped directory creation
        output_dirs = app.create_timestamped_output_directory()
        
        print("✅ Timestamped directory creation test passed")
        print(f"Session directory: {output_dirs['session_dir']}")
        print(f"Charts directory: {output_dirs['charts_dir']}")
        print(f"Reports directory: {output_dirs['reports_dir']}")
        print(f"Data directory: {output_dirs['data_dir']}")
        print(f"Timestamp: {output_dirs['timestamp']}")

        # Check if directories were created
        for dir_name, dir_path in output_dirs.items():
            if dir_name != 'timestamp':
                if dir_path.exists():
                    print(f"✅ {dir_name} created successfully: {dir_path}")
                else:
                    print(f"❌ {dir_name} not created: {dir_path}")

        return True
        
    except Exception as e:
        print(f"❌ Directory structure test failed: {e}")
        return False

def test_base_output_directory():
    """Test that the base output directory is in Documents"""
    try:
        from enhanced_main import EnhancedFinancialModelApp
        
        # Mock page class
        class MockPage:
            def __init__(self):
                self.title = ''
                self.theme_mode = None
                self.window_width = 1400
                self.window_height = 900
                self.scroll = None
            def add(self, content):
                pass
            def update(self):
                pass

        # Create a mock app instance
        page = MockPage()
        app = EnhancedFinancialModelApp(page)
        
        # Check base output directory
        expected_base = Path.home() / "Documents" / "Hiel_Financial_Reports"
        
        if app.base_output_dir == expected_base:
            print(f"✅ Base output directory correctly set to: {app.base_output_dir}")
            if app.base_output_dir.exists():
                print("✅ Base output directory exists")
            else:
                print("⚠️ Base output directory doesn't exist yet (will be created on first use)")
            return True
        else:
            print(f"❌ Base output directory incorrect. Expected: {expected_base}, Got: {app.base_output_dir}")
            return False
            
    except Exception as e:
        print(f"❌ Base output directory test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🔬 Testing New Features for Hiel Financial Model")
    print("=" * 55)
    
    tests = [
        ("Directory Structure", test_directory_structure),
        ("Base Output Directory", test_base_output_directory),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 Running test: {test_name}")
        print("-" * 30)
        
        if test_func():
            passed += 1
            print(f"✅ {test_name} PASSED")
        else:
            print(f"❌ {test_name} FAILED")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The new features are working correctly.")
        print("\n📁 New Features Summary:")
        print("✅ Charts generation path updated to Documents folder")
        print("✅ Each generation creates a separate timestamped folder")
        print("✅ Organized structure: charts/reports/data subfolders")
        print("✅ Comprehensive report generation includes DOCX + all charts + comparison charts")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
