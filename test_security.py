"""
Test script for the security screen
"""

import flet as ft
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from auth.security_screen import SecurityManager
from auth.config import SecurityConfig

def test_main(page: ft.Page):
    """Test the security screen"""
    
    def on_authenticated():
        page.clean()
        page.add(
            ft.Container(
                content=ft.Column([
                    ft.Text(
                        "🎉 Authentication Successful!",
                        size=32,
                        weight=ft.FontWeight.BOLD,
                        color=ft.Colors.GREEN_600
                    ),
                    ft.Text(
                        "Welcome to the Enhanced Financial Model",
                        size=18,
                        color=ft.Colors.GREY_700
                    ),
                    ft.Text(
                        f"Password used: {SecurityConfig.get_password()}",
                        size=14,
                        color=ft.Colors.BLUE_600
                    )
                ], 
                alignment=ft.MainAxisAlignment.CENTER,
                horizontal_alignment=ft.CrossAxisAlignment.CENTER),
                alignment=ft.alignment.center,
                expand=True
            )
        )
        page.update()
    
    # Initialize security manager
    security_manager = SecurityManager(page)
    security_manager.show_security_screen(on_authenticated)

if __name__ == "__main__":
    print(f"Default password: {SecurityConfig.get_password()}")
    ft.app(target=test_main)
