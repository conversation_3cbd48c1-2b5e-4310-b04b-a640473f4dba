#!/usr/bin/env python3
"""
Test script to verify chart export functionality
"""

import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path
from datetime import datetime

def test_chart_export():
    """Test basic chart export functionality"""
    
    # Create charts directory
    charts_dir = Path("charts")
    charts_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    print("🧪 Testing Chart Export Functionality...")
    
    try:
        # Test 1: Simple line chart
        plt.figure(figsize=(10, 6))
        years = np.arange(1, 26)
        cashflow = np.cumsum(np.random.normal(1, 0.5, 25))
        plt.plot(years, cashflow, marker='o', linewidth=2, color='navy')
        plt.title('Test: Cumulative Cash Flow', fontsize=16, fontweight='bold')
        plt.xlabel('Year')
        plt.ylabel('Million EUR')
        plt.grid(True, alpha=0.3)
        
        file1 = charts_dir / f"test_01_cashflow_{timestamp}.png"
        plt.savefig(file1, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ Line chart exported: {file1}")
        
        # Test 2: Pie chart with positive values
        plt.figure(figsize=(10, 8))
        sizes = [3.5, 5.9, 2.8]  # Positive values
        labels = ['Equity', 'Debt', 'Grants']
        colors = ['#4472C4', '#E67E22', '#27AE60']
        
        wedges, texts, autotexts = plt.pie(sizes, labels=labels, colors=colors, 
                                          autopct='%1.1f%%', startangle=90,
                                          textprops={'fontsize': 12, 'fontweight': 'bold'})
        plt.title('Test: Financing Structure', fontsize=16, fontweight='bold')
        
        file2 = charts_dir / f"test_02_pie_chart_{timestamp}.png"
        plt.savefig(file2, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ Pie chart exported: {file2}")
        
        # Test 3: Bar chart
        plt.figure(figsize=(12, 6))
        categories = ['Italian Grant', 'SIMEST', 'MASEN', 'Connection']
        values = [1.2, 0.5, 0.8, 0.3]
        colors = ['blue', 'navy', 'red', 'orange']
        
        bars = plt.bar(categories, values, color=colors)
        plt.title('Test: Grant Breakdown', fontsize=16, fontweight='bold')
        plt.ylabel('Amount (M EUR)')
        
        # Add value labels
        for bar, value in zip(bars, values):
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                    f'€{value:.1f}M', ha='center', va='bottom', fontweight='bold')
        
        file3 = charts_dir / f"test_03_bar_chart_{timestamp}.png"
        plt.savefig(file3, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ Bar chart exported: {file3}")
        
        # Test 4: Multi-panel dashboard
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        
        # Panel 1: KPIs
        kpis = ['Project IRR', 'Equity IRR', 'LCOE']
        kpi_values = [15.2, 18.5, 0.051]
        ax1.bar(kpis, kpi_values, color=['green', 'blue', 'orange'])
        ax1.set_title('Key Performance Indicators', fontweight='bold')
        
        # Panel 2: Financial structure
        fin_data = [8.5, 2.8, 2.5, 5.9]
        ax2.bar(['CAPEX', 'Grants', 'Equity', 'Debt'], fin_data, 
               color=['red', 'green', 'blue', 'orange'])
        ax2.set_title('Financial Structure (M EUR)', fontweight='bold')
        
        # Panel 3: Cash flow trend
        years_sample = years[:10]
        cf_trend = np.cumsum(np.random.normal(0.5, 0.2, 10))
        ax3.plot(years_sample, cf_trend, marker='o', linewidth=2, color='navy')
        ax3.set_title('Cash Flow Trend', fontweight='bold')
        ax3.grid(True, alpha=0.3)
        
        # Panel 4: Revenue growth
        revenue_trend = np.linspace(2.0, 2.5, 10) + np.random.normal(0, 0.05, 10)
        ax4.plot(years_sample, revenue_trend, marker='s', linewidth=2, color='green')
        ax4.set_title('Revenue Trend', fontweight='bold')
        ax4.grid(True, alpha=0.3)
        
        plt.suptitle('Test: Project Dashboard', fontsize=18, fontweight='bold')
        plt.tight_layout()
        
        file4 = charts_dir / f"test_04_dashboard_{timestamp}.png"
        plt.savefig(file4, dpi=300, bbox_inches='tight')
        plt.close()
        print(f"✅ Dashboard exported: {file4}")
        
        print(f"\n🎉 All chart exports successful!")
        print(f"📁 Charts saved to: {charts_dir.absolute()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chart export test failed: {str(e)}")
        return False

def test_docx_availability():
    """Test if python-docx is available"""
    try:
        from docx import Document
        print("✅ python-docx is available")
        return True
    except ImportError:
        print("❌ python-docx not available. Install with: pip install python-docx")
        return False

if __name__ == "__main__":
    print("🔬 Enhanced Financial Model - Export Testing")
    print("=" * 50)
    
    # Test DOCX availability
    docx_available = test_docx_availability()
    
    # Test chart export
    charts_working = test_chart_export()
    
    print("\n📊 TEST SUMMARY:")
    print(f"Charts Export: {'✅ WORKING' if charts_working else '❌ FAILED'}")
    print(f"DOCX Support: {'✅ AVAILABLE' if docx_available else '❌ NOT AVAILABLE'}")
    
    if charts_working and docx_available:
        print("\n🎯 All export functionality is ready!")
    elif charts_working:
        print("\n⚠️  Charts working, but DOCX needs python-docx installation")
    else:
        print("\n🚨 Chart export needs debugging")
