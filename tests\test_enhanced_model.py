#!/usr/bin/env python3
"""
Test script for Enhanced Renewable Energy Financial Model
This script validates the enhanced model functionality and benchmarks
"""

import sys
import traceback
from pathlib import Path
import pandas as pd
import numpy as np

# Add the core module to path
sys.path.append(str(Path(__file__).parent / "core"))

try:
    from core.enhanced_financial_model import (
        EnhancedAssumptions, build_enhanced_cashflow, compute_enhanced_kpis,
        build_enhanced_sensitivity, monte_carlo_simulation, generate_monte_carlo_statistics,
        run_enhanced_scenarios
    )
    from core.enhanced_data_models import (
        EnhancedProjectAssumptions, PREDEFINED_SCENARIOS
    )
    from core.model_validation import (
        validate_model_comprehensive, generate_benchmark_report
    )
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all dependencies are installed: pip install -r requirements_enhanced.txt")
    sys.exit(1)

def test_basic_functionality():
    """Test basic model functionality"""
    print("\n=== Testing Basic Functionality ===")
    
    try:
        # Create test assumptions
        assumptions = EnhancedProjectAssumptions(
            location_name="Test_Project",
            capacity_mw=10.0,
            capex_meur=8.5,
            production_mwh_year1=26000,
            ppa_price_eur_kwh=0.045
        )
        
        print(f"✓ Created assumptions for {assumptions.location_name}")
        print(f"  - Capacity: {assumptions.capacity_mw} MW")
        print(f"  - CAPEX: €{assumptions.capex_meur}M")
        print(f"  - Total Grants: €{assumptions.total_grants_meur}M")
        
        # Convert to EnhancedAssumptions for model
        assumptions_dict = assumptions.to_dict()
        
        # Map parameter names to match EnhancedAssumptions expectations
        if 'project_life_years' in assumptions_dict:
            assumptions_dict['years'] = assumptions_dict.pop('project_life_years')
        
        # Remove any parameters that don't exist in EnhancedAssumptions
        valid_params = {
            k: v for k, v in assumptions_dict.items() 
            if k in EnhancedAssumptions.__dataclass_fields__
        }
        
        enhanced_assumptions = EnhancedAssumptions(**valid_params)
        
        # Build cashflow
        cashflow = build_enhanced_cashflow(enhanced_assumptions)
        print(f"✓ Built cashflow with {len(cashflow)} years")
        
        # Compute KPIs
        kpis = compute_enhanced_kpis(cashflow, enhanced_assumptions)
        print(f"✓ Computed KPIs:")
        print(f"  - IRR Equity: {kpis.get('IRR_equity', 0):.1%}")
        print(f"  - NPV Equity: €{kpis.get('NPV_equity', 0):,.0f}")
        print(f"  - LCOE: {kpis.get('LCOE_eur_kwh', 0):.3f} €/kWh")
        print(f"  - Min DSCR: {kpis.get('Min_DSCR', 0):.2f}")
        
        return True, assumptions, cashflow, kpis
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        traceback.print_exc()
        return False, None, None, None

def test_validation_framework(assumptions, cashflow, kpis):
    """Test model validation framework"""
    print("\n=== Testing Validation Framework ===")
    
    try:
        # Run comprehensive validation
        validation_result = validate_model_comprehensive(assumptions, kpis, cashflow)
        
        print(f"✓ Validation completed")
        print(f"  - Valid: {validation_result.is_valid}")
        print(f"  - Warnings: {len(validation_result.warnings)}")
        print(f"  - Errors: {len(validation_result.errors)}")
        print(f"  - Recommendations: {len(validation_result.recommendations)}")
        
        if validation_result.warnings:
            print("  Warnings:")
            for warning in validation_result.warnings[:3]:  # Show first 3
                print(f"    - {warning}")
        
        if validation_result.errors:
            print("  Errors:")
            for error in validation_result.errors:
                print(f"    - {error}")
        
        # Generate benchmark report
        benchmark_report = generate_benchmark_report(kpis, assumptions)
        print(f"✓ Benchmark report generated")
        
        if 'rankings' in benchmark_report:
            print("  Rankings:")
            for metric, ranking in benchmark_report['rankings'].items():
                print(f"    - {metric}: {ranking}")
        
        return True
        
    except Exception as e:
        print(f"✗ Validation test failed: {e}")
        traceback.print_exc()
        return False

def test_sensitivity_analysis(assumptions):
    """Test sensitivity analysis"""
    print("\n=== Testing Sensitivity Analysis ===")
    
    try:
        # Convert assumptions with parameter mapping
        assumptions_dict = assumptions.to_dict()
        if 'project_life_years' in assumptions_dict:
            assumptions_dict['years'] = assumptions_dict.pop('project_life_years')
        valid_params = {
            k: v for k, v in assumptions_dict.items() 
            if k in EnhancedAssumptions.__dataclass_fields__
        }
        enhanced_assumptions = EnhancedAssumptions(**valid_params)
        
        # Test sensitivity analysis
        variables = ['production_mwh_year1', 'ppa_price_eur_kwh', 'capex_meur']
        sensitivity_df = build_enhanced_sensitivity(enhanced_assumptions, variables)
        
        print(f"✓ Sensitivity analysis completed")
        print(f"  - Variables tested: {len(variables)}")
        print(f"  - Scenarios per variable: {len(sensitivity_df) // len(variables)}")
        print(f"  - Total scenarios: {len(sensitivity_df)}")
        
        # Show sample results
        if len(sensitivity_df) > 0:
            print("  Sample results:")
            for var in variables[:2]:  # Show first 2 variables
                var_data = sensitivity_df[sensitivity_df['Variable'] == var]
                if len(var_data) > 0:
                    base_irr = var_data[var_data['Delta'] == '+0.0%']['IRR_equity'].iloc[0] if '+0.0%' in var_data['Delta'].values else None
                    if base_irr is not None:
                        print(f"    - {var}: Base IRR {base_irr:.1%}")
        
        return True
        
    except Exception as e:
        print(f"✗ Sensitivity analysis test failed: {e}")
        traceback.print_exc()
        return False

def test_monte_carlo(assumptions):
    """Test Monte Carlo simulation"""
    print("\n=== Testing Monte Carlo Simulation ===")
    
    try:
        # Convert assumptions with parameter mapping
        assumptions_dict = assumptions.to_dict()
        if 'project_life_years' in assumptions_dict:
            assumptions_dict['years'] = assumptions_dict.pop('project_life_years')
        valid_params = {
            k: v for k, v in assumptions_dict.items() 
            if k in EnhancedAssumptions.__dataclass_fields__
        }
        enhanced_assumptions = EnhancedAssumptions(**valid_params)
        
        # Run smaller Monte Carlo for testing (100 simulations)
        mc_results = monte_carlo_simulation(enhanced_assumptions, n_simulations=100)
        mc_stats = generate_monte_carlo_statistics(mc_results)
        
        print(f"✓ Monte Carlo simulation completed")
        print(f"  - Simulations run: 100")
        print(f"  - Metrics analyzed: {len(mc_stats)}")
        
        # Show statistics for IRR
        if 'IRR_equity' in mc_stats:
            irr_stats = mc_stats['IRR_equity']
            print(f"  IRR Equity Statistics:")
            print(f"    - Mean: {irr_stats['mean']:.1%}")
            print(f"    - Std Dev: {irr_stats['std']:.1%}")
            print(f"    - 5th percentile: {irr_stats['p5']:.1%}")
            print(f"    - 95th percentile: {irr_stats['p95']:.1%}")
        
        return True
        
    except Exception as e:
        print(f"✗ Monte Carlo test failed: {e}")
        traceback.print_exc()
        return False

def test_scenarios():
    """Test scenario analysis"""
    print("\n=== Testing Scenario Analysis ===")
    
    try:
        # Run enhanced scenarios
        scenarios = run_enhanced_scenarios()
        
        print(f"✓ Scenario analysis completed")
        print(f"  - Scenarios run: {len(scenarios)}")
        
        # Show results for each scenario
        for scenario_name, results in scenarios.items():
            irr = results.get('IRR_equity', 0)
            npv = results.get('NPV_equity', 0)
            lcoe = results.get('LCOE_eur_kwh', 0)
            print(f"  {scenario_name}:")
            print(f"    - IRR: {irr:.1%}, NPV: €{npv:,.0f}, LCOE: {lcoe:.3f} €/kWh")
        
        return True
        
    except Exception as e:
        print(f"✗ Scenario analysis test failed: {e}")
        traceback.print_exc()
        return False

def test_data_models():
    """Test enhanced data models"""
    print("\n=== Testing Enhanced Data Models ===")
    
    try:
        # Test predefined scenarios
        print(f"✓ Predefined scenarios available: {len(PREDEFINED_SCENARIOS)}")
        for name, scenario in PREDEFINED_SCENARIOS.items():
            print(f"  - {name}: {scenario.description}")
        
        # Test scenario application
        base_assumptions = EnhancedProjectAssumptions()
        conservative_scenario = PREDEFINED_SCENARIOS['conservative']
        modified_assumptions = conservative_scenario.apply_to_assumptions(base_assumptions)
        
        print(f"✓ Scenario application test:")
        print(f"  - Base production: {base_assumptions.production_mwh_year1:,.0f} MWh")
        print(f"  - Conservative production: {modified_assumptions.production_mwh_year1:,.0f} MWh")
        print(f"  - Multiplier applied: {conservative_scenario.production_multiplier}")
        
        return True
        
    except Exception as e:
        print(f"✗ Data models test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("Enhanced Renewable Energy Financial Model - Test Suite")
    print("=" * 60)
    
    tests_passed = 0
    total_tests = 6
    
    # Test 1: Basic functionality
    success, assumptions, cashflow, kpis = test_basic_functionality()
    if success:
        tests_passed += 1
    
    if success:  # Only continue if basic test passed
        # Test 2: Validation framework
        if test_validation_framework(assumptions, cashflow, kpis):
            tests_passed += 1
        
        # Test 3: Sensitivity analysis
        if test_sensitivity_analysis(assumptions):
            tests_passed += 1
        
        # Test 4: Monte Carlo
        if test_monte_carlo(assumptions):
            tests_passed += 1
    
    # Test 5: Scenarios (independent)
    if test_scenarios():
        tests_passed += 1
    
    # Test 6: Data models (independent)
    if test_data_models():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 60)
    print(f"TEST SUMMARY: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✓ All tests passed! Enhanced model is ready for use.")
        print("\nNext steps:")
        print("1. Run 'python enhanced_main.py' to start the application")
        print("2. Review ENHANCED_MODEL_DOCUMENTATION.md for detailed information")
        print("3. Validate grant assumptions with official sources")
        return True
    else:
        print(f"✗ {total_tests - tests_passed} tests failed. Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Ensure all dependencies are installed: pip install -r requirements_enhanced.txt")
        print("2. Check Python version (3.9+ required)")
        print("3. Review error messages for specific issues")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)