#!/usr/bin/env python3
"""
Test DOCX export functionality
"""

def test_docx_availability():
    """Test if python-docx is available and working"""
    try:
        from docx import Document
        from docx.shared import Inches, Pt
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.enum.table import WD_TABLE_ALIGNMENT
        
        print("✅ python-docx imports successful")
        
        # Test basic document creation
        doc = Document()
        doc.add_heading('Test Document', 0)
        doc.add_paragraph('This is a test paragraph.')
        
        # Test table creation
        table = doc.add_table(rows=1, cols=2)
        table.style = 'Table Grid'
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = 'Parameter'
        hdr_cells[1].text = 'Value'
        
        # Add a row
        row_cells = table.add_row().cells
        row_cells[0].text = 'Test Parameter'
        row_cells[1].text = 'Test Value'
        
        # Save test document
        filename = 'test_docx_export.docx'
        doc.save(filename)
        
        print(f"✅ Test DOCX document created: {filename}")
        return True
        
    except ImportError as e:
        print(f"❌ python-docx not available: {e}")
        print("Install with: pip install python-docx")
        return False
    except Exception as e:
        print(f"❌ DOCX test failed: {e}")
        return False

def create_sample_financial_report():
    """Create a sample financial report to test the functionality"""
    try:
        from docx import Document
        from docx.shared import Inches
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        from docx.enum.table import WD_TABLE_ALIGNMENT
        from datetime import datetime
        
        # Create new document
        doc = Document()
        
        # Add title
        title = doc.add_heading('Enhanced Financial Model Report', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add subtitle
        subtitle = doc.add_heading('Sample Project: Morocco Solar PV', level=1)
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Add executive summary
        doc.add_heading('Executive Summary', level=1)
        
        # Project details table
        table = doc.add_table(rows=1, cols=2)
        table.style = 'Table Grid'
        table.alignment = WD_TABLE_ALIGNMENT.CENTER
        
        # Header row
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = 'Parameter'
        hdr_cells[1].text = 'Value'
        
        # Add project data
        project_data = [
            ('Project Name', 'Morocco Solar PV Project'),
            ('Technology', 'Solar Photovoltaic'),
            ('Capacity', '10.0 MW'),
            ('Location', 'Morocco'),
            ('Total Investment', '€8.5M'),
            ('Grant Support', '€2.8M (32.9% of CAPEX)'),
            ('Project Life', '25 years'),
            ('Annual Production (Year 1)', '22,000 MWh'),
            ('PPA Price', '0.055 EUR/kWh')
        ]
        
        for param, value in project_data:
            row_cells = table.add_row().cells
            row_cells[0].text = param
            row_cells[1].text = str(value)
        
        # Add KPIs section
        doc.add_heading('Key Performance Indicators', level=1)
        
        kpi_table = doc.add_table(rows=1, cols=2)
        kpi_table.style = 'Table Grid'
        kpi_table.alignment = WD_TABLE_ALIGNMENT.CENTER
        
        # KPI header
        kpi_hdr = kpi_table.rows[0].cells
        kpi_hdr[0].text = 'KPI'
        kpi_hdr[1].text = 'Value'
        
        # Add KPI data
        kpi_data = [
            ('Project IRR', '15.2%'),
            ('Equity IRR', '18.5%'),
            ('LCOE', '0.051 EUR/kWh'),
            ('NPV (Project)', '€7.3M'),
            ('NPV (Equity)', '€4.1M'),
            ('Minimum DSCR', '1.25'),
            ('Payback Period', '8.5 years')
        ]
        
        for kpi, value in kpi_data:
            kpi_row = kpi_table.add_row().cells
            kpi_row[0].text = kpi
            kpi_row[1].text = value
        
        # Add financing structure
        doc.add_heading('Financing Structure', level=1)
        
        financing_table = doc.add_table(rows=1, cols=3)
        financing_table.style = 'Table Grid'
        financing_table.alignment = WD_TABLE_ALIGNMENT.CENTER
        
        fin_hdr = financing_table.rows[0].cells
        fin_hdr[0].text = 'Source'
        fin_hdr[1].text = 'Amount (€M)'
        fin_hdr[2].text = 'Percentage'
        
        financing_data = [
            ('Equity', '2.8', '32.9%'),
            ('Debt', '2.9', '34.1%'),
            ('Grants', '2.8', '32.9%'),
            ('TOTAL', '8.5', '100.0%')
        ]
        
        for source, amount, percentage in financing_data:
            fin_row = financing_table.add_row().cells
            fin_row[0].text = source
            fin_row[1].text = amount
            fin_row[2].text = percentage
        
        # Add analysis section
        doc.add_heading('Financial Analysis', level=1)
        
        analysis_text = """
This renewable energy project in Morocco demonstrates strong financial viability with the following key highlights:

• The project achieves an equity IRR of 18.5%, which exceeds typical investor return requirements for emerging market renewable energy projects (12-15%).

• The LCOE of 0.051 EUR/kWh is competitive with regional benchmarks and supports long-term project sustainability.

• Grant funding totaling €2.8M (32.9% of CAPEX) significantly improves project economics, reducing the equity requirement and enhancing returns.

• The minimum DSCR of 1.25 meets typical lender covenant requirements.

• The project benefits from Morocco's supportive renewable energy framework and Italy's strategic focus on African energy investments through the Mattei Plan.
"""
        
        doc.add_paragraph(analysis_text)
        
        # Add footer with timestamp
        doc.add_page_break()
        footer_para = doc.add_paragraph()
        footer_para.add_run(f'Report generated on {datetime.now().strftime("%B %d, %Y at %H:%M")}')
        footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Save document
        filename = 'sample_financial_report.docx'
        doc.save(filename)
        
        print(f"✅ Sample financial report created: {filename}")
        return True
        
    except Exception as e:
        print(f"❌ Sample report creation failed: {e}")
        return False

if __name__ == "__main__":
    print("🔬 Testing DOCX Export Functionality")
    print("=" * 40)
    
    # Test basic DOCX availability
    docx_works = test_docx_availability()
    
    if docx_works:
        print("\n📄 Creating sample financial report...")
        sample_works = create_sample_financial_report()
        
        if sample_works:
            print("\n🎉 DOCX export functionality is working!")
            print("✅ Both basic and financial report creation successful")
        else:
            print("\n⚠️ Basic DOCX works, but financial report creation failed")
    else:
        print("\n🚨 DOCX functionality not available")
        print("Please install python-docx: pip install python-docx")
