"""
Security configuration for the Enhanced Financial Model
"""

import os
import json
from pathlib import Path

class SecurityConfig:
    """Manages security configuration"""
    
    # Default settings
    DEFAULT_PASSWORD = "Agevolami2025"
    CONFIG_FILE = "security_config.json"
    
    @classmethod
    def get_password(cls) -> str:
        """Get the configured password"""
        config_path = Path(cls.CONFIG_FILE)
        
        if config_path.exists():
            try:
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    return config.get('password', cls.DEFAULT_PASSWORD)
            except (json.<PERSON><PERSON>NDecodeError, IOError):
                pass
        
        return cls.DEFAULT_PASSWORD
    
    @classmethod
    def set_password(cls, password: str) -> bool:
        """Set a new password"""
        try:
            config = {'password': password}
            with open(cls.CONFIG_FILE, 'w') as f:
                json.dump(config, f, indent=2)
            return True
        except IOError:
            return False
    
    @classmethod
    def get_developer_info(cls) -> dict:
        """Get developer information"""
        return {
            'name': '<PERSON><PERSON><PERSON><PERSON>',
            'title': 'Financial & Business Consultant',
            'company': 'Agevolami.it/ma',
            'specializations': [
                'Renewable Energy',
                'Financial Modeling', 
                'Italy-Morocco Projects'
            ],
            'year': '2024'
        }
