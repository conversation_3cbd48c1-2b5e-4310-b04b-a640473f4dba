#!/usr/bin/env python3
"""
Simple test to verify LCOE values are consistent
"""

def test_lcoe_values():
    """Test LCOE values from your images"""
    
    print("🔍 LCOE Synchronization Analysis")
    print("=" * 50)
    
    # Values from your images
    chart1_raw_lcoe = 0.109  # From comparison chart
    chart1_project_lcoe = 0.082  # From comparison chart
    
    chart2_raw_lcoe = 0.1086  # From waterfall chart
    chart2_final_lcoe = 0.0737  # From waterfall chart
    
    app_raw_lcoe = 0.109  # From app interface
    app_project_lcoe = 0.082  # From app interface
    
    print("📊 Current LCOE Values:")
    print(f"   Chart 1 (Comparison): RAW={chart1_raw_lcoe:.4f}, PROJECT={chart1_project_lcoe:.4f}")
    print(f"   Chart 2 (Waterfall):  RAW={chart2_raw_lcoe:.4f}, FINAL={chart2_final_lcoe:.4f}")
    print(f"   App Interface:        RAW={app_raw_lcoe:.4f}, PROJECT={app_project_lcoe:.4f}")
    
    print("\n🔍 Inconsistencies Found:")
    
    # Check RAW LCOE consistency
    if abs(chart1_raw_lcoe - chart2_raw_lcoe) > 0.001:
        print(f"   ❌ RAW LCOE mismatch: {chart1_raw_lcoe:.4f} vs {chart2_raw_lcoe:.4f}")
    else:
        print(f"   ✅ RAW LCOE consistent: {chart1_raw_lcoe:.4f}")
    
    # Check Final LCOE consistency
    if abs(chart1_project_lcoe - chart2_final_lcoe) > 0.001:
        print(f"   ❌ FINAL LCOE mismatch: {chart1_project_lcoe:.4f} vs {chart2_final_lcoe:.4f}")
        print(f"      Difference: {abs(chart1_project_lcoe - chart2_final_lcoe):.4f} EUR/kWh")
    else:
        print(f"   ✅ FINAL LCOE consistent: {chart1_project_lcoe:.4f}")
    
    # Calculate reductions
    reduction1 = chart1_raw_lcoe - chart1_project_lcoe
    reduction2 = chart2_raw_lcoe - chart2_final_lcoe
    
    print(f"\n📈 LCOE Reductions:")
    print(f"   Chart 1: {reduction1:.4f} EUR/kWh ({reduction1/chart1_raw_lcoe*100:.1f}%)")
    print(f"   Chart 2: {reduction2:.4f} EUR/kWh ({reduction2/chart2_raw_lcoe*100:.1f}%)")
    
    print(f"\n🎯 Target Synchronized Values:")
    print(f"   RAW LCOE: 0.109 EUR/kWh (use this consistently)")
    print(f"   FINAL LCOE: 0.082 EUR/kWh (use this consistently)")
    print(f"   Total Reduction: 0.027 EUR/kWh (24.8% savings)")
    
    return {
        'target_raw_lcoe': 0.109,
        'target_final_lcoe': 0.082,
        'target_reduction': 0.027,
        'target_percentage': 24.8
    }

if __name__ == "__main__":
    results = test_lcoe_values()
    print(f"\n✅ Use these synchronized values across all charts and displays!")
